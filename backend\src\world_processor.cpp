
#include "world_processor.h"
#include <chrono>
#include <cmath>
#include <algorithm>
#include <cstring>
#include "../lib/world/src/world/dio.h"
#include "../lib/world/src/world/cheaptrick.h"
#include "../lib/world/src/world/d4c.h"
#include "../lib/world/src/world/stonemask.h"
#include "../lib/world/src/world/synthesis.h"

WorldProcessor::WorldProcessor(int sampleRate, int frameSize, bool realTimeMode)
    : sampleRate_(sampleRate), frameSize_(frameSize), realTimeMode_(realTimeMode),
      processingLatency_(0.0), randomGenerator_(std::random_device{}()),
      noiseDistribution_(-1.0f, 1.0f)
{

    // Calculate WORLD parameters
    framePeriod_ = static_cast<double>(frameSize) / sampleRate * 1000.0; // in ms
    // Use CheapTrick's FFT size calculation as per WORLD API
    CheapTrickOption cheapTrickOption;
    InitializeCheapTrickOption(sampleRate_, &cheapTrickOption);
    fftSize_ = GetFFTSizeForCheapTrick(sampleRate_, &cheapTrickOption);

    // Initialize buffers
    InitializeBuffers();
}

WorldProcessor::~WorldProcessor()
{
    CleanupBuffers();
}

void WorldProcessor::InitializeBuffers()
{
    // Pre-allocate buffers for real-time processing
    audioBuffer_.resize(frameSize_);
    f0Buffer_.resize(frameSize_ / 80 + 1); // Approximate F0 frames
    timeAxis_.resize(f0Buffer_.size());
    synthesizedBuffer_.resize(frameSize_);

    // Initialize time axis
    for (size_t i = 0; i < timeAxis_.size(); i++)
    {
        timeAxis_[i] = i * framePeriod_ / 1000.0;
    }

    // Allocate 2D buffers for spectral envelope and aperiodicity
    int spectralBins = fftSize_ / 2 + 1;
    spectralEnvelopeBuffer_.resize(timeAxis_.size());
    aperiodicityBuffer_.resize(timeAxis_.size());

    for (size_t i = 0; i < timeAxis_.size(); i++)
    {
        spectralEnvelopeBuffer_[i] = new double[spectralBins];
        aperiodicityBuffer_[i] = new double[spectralBins];
    }
}

void WorldProcessor::CleanupBuffers()
{
    for (auto *buffer : spectralEnvelopeBuffer_)
    {
        delete[] buffer;
    }
    for (auto *buffer : aperiodicityBuffer_)
    {
        delete[] buffer;
    }
    spectralEnvelopeBuffer_.clear();
    aperiodicityBuffer_.clear();
}

std::vector<float> WorldProcessor::ProcessFrame(const float *audioData, size_t audioLength,
                                                const VoiceMorphProfile &profile)
{
    auto startTime = std::chrono::high_resolution_clock::now();

    // Validate input parameters
    if (!audioData || audioLength == 0)
    {
        throw std::invalid_argument("Invalid audio data: null pointer or zero length");
    }

    if (audioLength > sampleRate_ * 10)
    { // More than 10 seconds
        throw std::invalid_argument("Audio data too large: " + std::to_string(audioLength) + " samples");
    }

    // Check for reasonable audio values
    bool hasValidSamples = false;
    for (size_t i = 0; i < std::min(audioLength, static_cast<size_t>(1000)); i++)
    {
        if (std::abs(audioData[i]) > 0.001f)
        {
            hasValidSamples = true;
            break;
        }
    }

    if (!hasValidSamples)
    {
        // Return silence if input is essentially silent
        return std::vector<float>(audioLength, 0.0f);
    }

    try
    {
        // Extract WORLD features
        WorldFeatures features = ExtractFeatures(audioData, audioLength);

        // Apply voice morphing
        WorldFeatures morphedFeatures = ApplyMorphing(features, profile);

        // Synthesize morphed audio
        std::vector<float> morphedAudio = SynthesizeAudio(morphedFeatures);

        // Apply anti-forensic processing if enabled
        if (profile.antiForensic)
        {
            ApplyAntiForensicProcessing(morphedAudio, profile);
        }

        // Calculate processing latency
        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(endTime - startTime);
        processingLatency_ = duration.count() / 1000.0; // Convert to milliseconds

        return morphedAudio;
    }
    catch (const std::exception &e)
    {
        // Log error and return original audio on error
        std::cerr << "WORLD processing error: " << e.what() << std::endl;
        std::vector<float> fallbackAudio(audioData, audioData + audioLength);
        return fallbackAudio;
    }
}

WorldFeatures WorldProcessor::ExtractFeatures(const float *audioData, size_t audioLength)
{
    WorldFeatures features;

    // Validate buffer sizes
    if (audioLength > audioBuffer_.size())
    {
        // Resize buffers if needed
        audioBuffer_.resize(audioLength);

        // Recalculate frame count
        size_t frameCount = static_cast<size_t>(audioLength / (framePeriod_ * sampleRate_ / 1000.0)) + 1;
        f0Buffer_.resize(frameCount);
        timeAxis_.resize(frameCount);

        // Update time axis
        for (size_t i = 0; i < timeAxis_.size(); i++)
        {
            timeAxis_[i] = i * framePeriod_ / 1000.0;
        }

        // Resize 2D buffers
        for (auto *buffer : spectralEnvelopeBuffer_)
        {
            delete[] buffer;
        }
        for (auto *buffer : aperiodicityBuffer_)
        {
            delete[] buffer;
        }
        spectralEnvelopeBuffer_.clear();
        aperiodicityBuffer_.clear();

        int spectralBins = fftSize_ / 2 + 1;
        spectralEnvelopeBuffer_.resize(frameCount);
        aperiodicityBuffer_.resize(frameCount);

        for (size_t i = 0; i < frameCount; i++)
        {
            spectralEnvelopeBuffer_[i] = new double[spectralBins];
            aperiodicityBuffer_[i] = new double[spectralBins];
        }
    }

    // Convert float to double
    ConvertFloatToDouble(audioData, audioBuffer_.data(), audioLength);

    // F0 estimation using DIO (faster) or Harvest (more accurate)
    DioOption dioOption;
    InitializeDioOption(&dioOption);
    dioOption.frame_period = framePeriod_;
    dioOption.speed = realTimeMode_ ? 2 : 1;

    // Ensure we don't exceed buffer bounds
    int frameCount = static_cast<int>(std::min(timeAxis_.size(), f0Buffer_.size()));
    Dio(audioBuffer_.data(), static_cast<int>(audioLength), sampleRate_, &dioOption,
        timeAxis_.data(), f0Buffer_.data());

    // Refine F0 using StoneMask
    StoneMask(audioBuffer_.data(), static_cast<int>(audioLength), sampleRate_,
              timeAxis_.data(), f0Buffer_.data(), static_cast<int>(timeAxis_.size()),
              f0Buffer_.data());

    // Spectral envelope estimation using CheapTrick
    CheapTrickOption cheapTrickOption;
    InitializeCheapTrickOption(sampleRate_, &cheapTrickOption);
    cheapTrickOption.q1 = realTimeMode_ ? -0.09 : -0.15;
    CheapTrick(audioBuffer_.data(), static_cast<int>(audioLength), sampleRate_,
               timeAxis_.data(), f0Buffer_.data(), static_cast<int>(timeAxis_.size()),
               &cheapTrickOption, spectralEnvelopeBuffer_.data());

    // Aperiodicity estimation using D4C
    D4COption d4cOption;
    InitializeD4COption(&d4cOption);
    d4cOption.threshold = realTimeMode_ ? 0.85 : 0.75;
    D4C(audioBuffer_.data(), static_cast<int>(audioLength), sampleRate_,
        timeAxis_.data(), f0Buffer_.data(), static_cast<int>(timeAxis_.size()),
        fftSize_, &d4cOption, aperiodicityBuffer_.data());

    // Copy results to features structure
    features.f0.assign(f0Buffer_.begin(), f0Buffer_.begin() + timeAxis_.size());
    features.frameLength = static_cast<int>(timeAxis_.size());
    features.fftSize = fftSize_;

    int spectralBins = fftSize_ / 2 + 1;
    features.spectralEnvelope.resize(timeAxis_.size());
    features.aperiodicity.resize(timeAxis_.size());

    for (size_t i = 0; i < timeAxis_.size(); i++)
    {
        features.spectralEnvelope[i].assign(spectralEnvelopeBuffer_[i],
                                            spectralEnvelopeBuffer_[i] + spectralBins);
        features.aperiodicity[i].assign(aperiodicityBuffer_[i],
                                        aperiodicityBuffer_[i] + spectralBins);
    }

    return features;
}

std::vector<float> WorldProcessor::SynthesizeAudio(const WorldFeatures &features)
{
    // Prepare synthesis parameters
    int outputLength = static_cast<int>(features.frameLength * framePeriod_ / 1000.0 * sampleRate_);
    synthesizedBuffer_.resize(outputLength);

    // Copy features to buffers
    std::copy(features.f0.begin(), features.f0.end(), f0Buffer_.data());

    int spectralBins = fftSize_ / 2 + 1;
    for (size_t i = 0; i < features.spectralEnvelope.size(); i++)
    {
        std::copy(features.spectralEnvelope[i].begin(), features.spectralEnvelope[i].end(),
                  spectralEnvelopeBuffer_[i]);
        std::copy(features.aperiodicity[i].begin(), features.aperiodicity[i].end(),
                  aperiodicityBuffer_[i]);
    }

    // Synthesize audio using WORLD
    Synthesis(f0Buffer_.data(), features.frameLength,
              spectralEnvelopeBuffer_.data(), aperiodicityBuffer_.data(),
              fftSize_, framePeriod_, sampleRate_, outputLength, synthesizedBuffer_.data());

    // Convert double to float
    std::vector<float> result(outputLength);
    ConvertDoubleToFloat(synthesizedBuffer_.data(), result.data(), outputLength);

    return result;
}

WorldFeatures WorldProcessor::ApplyMorphing(const WorldFeatures &features,
                                            const VoiceMorphProfile &profile)
{
    WorldFeatures morphedFeatures = features;

    // Apply pitch scaling
    for (auto &f0 : morphedFeatures.f0)
    {
        if (f0 > 0)
        { // Only modify voiced frames
            f0 *= profile.pitchScale;
        }
    }

    // Apply spectral warping
    if (std::abs(profile.spectralWarp) > 0.001f)
    {
        ApplySpectralWarping(morphedFeatures.spectralEnvelope, profile.spectralWarp);
    }

    // Apply EQ tilt
    if (std::abs(profile.eqTilt) > 0.001f)
    {
        ApplyEQTilt(morphedFeatures.spectralEnvelope, profile.eqTilt);
    }

    // Apply temporal jitter for anti-forensic processing
    if (profile.temporalJitter > 0.001f)
    {
        ApplyTemporalJitter(morphedFeatures.f0, profile.temporalJitter);
    }

    // Apply spectral noise for irreversible transformation
    if (profile.spectralNoise > 0.001f)
    {
        ApplySpectralNoise(morphedFeatures.spectralEnvelope, profile.spectralNoise);
    }

    return morphedFeatures;
}

void WorldProcessor::ApplySpectralWarping(std::vector<std::vector<double>> &spectralEnvelope,
                                          float warpFactor)
{
    // Simple spectral warping by frequency shifting
    for (auto &frame : spectralEnvelope)
    {
        std::vector<double> warpedFrame(frame.size());

        for (size_t i = 0; i < frame.size(); i++)
        {
            double warpedIndex = i * (1.0 + warpFactor / 100.0);
            int lowerIndex = static_cast<int>(warpedIndex);
            int upperIndex = lowerIndex + 1;

            if (upperIndex < static_cast<int>(frame.size()))
            {
                double fraction = warpedIndex - lowerIndex;
                warpedFrame[i] = frame[lowerIndex] * (1.0 - fraction) +
                                 frame[upperIndex] * fraction;
            }
            else
            {
                warpedFrame[i] = frame.back();
            }
        }

        frame = warpedFrame;
    }
}

void WorldProcessor::ApplyTemporalJitter(std::vector<double> &f0, float jitterAmount)
{
    for (auto &freq : f0)
    {
        if (freq > 0)
        {
            float jitter = noiseDistribution_(randomGenerator_) * jitterAmount;
            freq *= (1.0 + jitter);
        }
    }
}

void WorldProcessor::ApplySpectralNoise(std::vector<std::vector<double>> &spectralEnvelope,
                                        float noiseAmount)
{
    for (auto &frame : spectralEnvelope)
    {
        for (auto &bin : frame)
        {
            float noise = noiseDistribution_(randomGenerator_) * noiseAmount;
            bin *= (1.0 + noise);
        }
    }
}

void WorldProcessor::ApplyEQTilt(std::vector<std::vector<double>> &spectralEnvelope,
                                 float tiltAmount)
{
    for (auto &frame : spectralEnvelope)
    {
        for (size_t i = 0; i < frame.size(); i++)
        {
            double frequency = static_cast<double>(i) / frame.size() * sampleRate_ / 2.0;
            double tiltFactor = std::pow(frequency / 1000.0, tiltAmount / 6.0);
            frame[i] *= tiltFactor;
        }
    }
}

void WorldProcessor::ApplyAntiForensicProcessing(std::vector<float> &audioData,
                                                 const VoiceMorphProfile &profile)
{
    // Apply reverb effect
    if (profile.reverbAmount > 0.001f)
    {
        ApplyReverb(audioData, profile.reverbAmount);
    }

    // Add subtle random noise to prevent reconstruction
    for (auto &sample : audioData)
    {
        float noise = noiseDistribution_(randomGenerator_) * 0.001f * profile.spectralNoise;
        sample += noise;

        // Clamp to prevent clipping
        sample = std::max(-1.0f, std::min(1.0f, sample));
    }
}

void WorldProcessor::ApplyReverb(std::vector<float> &audioData, float reverbAmount)
{
    // Simple reverb implementation using delay lines
    const int delayLength = static_cast<int>(sampleRate_ * 0.05); // 50ms delay
    std::vector<float> delayBuffer(delayLength, 0.0f);
    int delayIndex = 0;

    float reverbGain = reverbAmount / 100.0f * 0.3f;

    for (auto &sample : audioData)
    {
        float delayedSample = delayBuffer[delayIndex];
        delayBuffer[delayIndex] = sample + delayedSample * 0.5f;
        sample += delayedSample * reverbGain;

        delayIndex = (delayIndex + 1) % delayLength;
    }
}

double WorldProcessor::GetCurrentTimeMs() const
{
    auto now = std::chrono::high_resolution_clock::now();
    auto duration = now.time_since_epoch();
    return std::chrono::duration_cast<std::chrono::microseconds>(duration).count() / 1000.0;
}

void WorldProcessor::ConvertFloatToDouble(const float *input, double *output, size_t length)
{
    for (size_t i = 0; i < length; i++)
    {
        output[i] = static_cast<double>(input[i]);
    }
}

void WorldProcessor::ConvertDoubleToFloat(const double *input, float *output, size_t length)
{
    for (size_t i = 0; i < length; i++)
    {
        output[i] = static_cast<float>(input[i]);
    }
}
